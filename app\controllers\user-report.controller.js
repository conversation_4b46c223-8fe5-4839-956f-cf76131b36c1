const mongoose = require('mongoose');
// Services
const userReportService = require('../services/user-report.service');
const userReportAnswerService = require('../services/user-report-answer.service');
const userProjectReportService = require('../services/user-project-report.service');
const memberService = require('../services/member.service');
const projectServices = require('../services/project.service');

// controller
const {
  checkReportQuestionAnswer,
  checkIsPrintableValueInExistingAnswer,
} = require('./user-report-answer.controller');

// Utils
const constantUtils = require('../utils/constants.utils');
const { successResponse, errorResponse } = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const commonfunctionUtils = require('../utils/common-function.utils');
const { exportReportPDF } = require('../services/pdf-template.service');
const { transactionOptions } = require('../utils/json-format.utils');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Create User Report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createUserReport = async (req, res) => {
  try {
    let reqData = req.body;

    // check and create user project report
    reqData.userProjectReport = await userProjectReportService.checkAndCreateUserProjectReport(
      reqData,
      req.userData.account,
      req.userData._id
    );

    // create user report
    reqData.account = req.userData.account;
    reqData.createdBy = req.userData._id;

    const responseData = await userReportService.createUserReport(reqData);

    // update sync api manage data
    if (responseData) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res.status(200).json(successResponse(constantUtils.CREATE_USER_REPORT, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get User Report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */

exports.getUserReports = async (req, res) => {
  try {
    let page = req.query.page ?? null;
    let perPage = req.query.perPage ?? null;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    let filter = {
      ...(req.query.type && { createdBy: req.userData._id }),
      ...(req.query.status && { status: req.query.status }),
      account: req.userData.account,
      deletedAt: null,
    };

    let { projectStatus } = req.query;

    // Get all projects by status
    if (projectStatus) {
      const projectList = await projectServices.getAllProjects({
        status: projectStatus,
        account: req.userData.account,
        deletedAt: null,
      });

      if (projectList && projectList.length > 0) {
        filter = {
          ...filter,
          project: {
            $in: projectList.map(project => project._id),
          },
        };
      }
    }

    let userReportsIds = await userReportService.userReports(filter, page, perPage, sort);
    // eslint-disable-next-line no-undef
    let responseData = await Promise.all(
      userReportsIds.map(async item => {
        let [userReportDetails] = await userReportService.userReportsDetails(item._id);
        let [questions] = await userReportService.userReportsQuestions(item._id);
        if (userReportDetails && questions) {
          const reportQuestions = questions.reportQuestions;
          userReportDetails = { ...userReportDetails, reportQuestions };
          item = userReportDetails;
          return item;
        }
      })
    );
    responseData = responseData.filter(id => id?._id);

    return res.status(200).json(successResponse(constantUtils.GET_USER_REPORTS, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get User Report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getReports = async (req, res) => {
  try {
    let { projectStatus, project } = req.query;
    let { filter, page, perPage, sort } = await commonfunctionUtils.getFilterAndPaginationParams(
      req
    );
    // Add project filter if project status is provided
    if (projectStatus && project === 'all') {
      filter = await commonUtils.filterProjectStatus(projectStatus, req.userData.account, filter);
    }

    delete filter?.projectStatus;

    const responseData = await userReportService.getReports(filter, page, perPage, sort);

    // add all records count
    let getAllRecords = await userReportService.getReports(filter, null, null, sort);

    let finalResponse = {
      reportData: responseData,
      currentPage: Number(page),
      allRecordsCount: getAllRecords.length,
    };

    return res.status(200).json(successResponse(constantUtils.GET_REPORT, finalResponse));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get User Report - v2 optimized
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getReportsOptimized = async (req, res) => {
  try {
    let { projectStatus, project } = req.query;
    let { filter, page, perPage, sort } = await commonfunctionUtils.getFilterAndPaginationParams(
      req
    );
    // Add project filter if project status is provided
    if (projectStatus && project === 'all') {
      filter = await commonUtils.filterProjectStatus(
        projectStatus,
        req.userData.account,
        filter,
        req?.assignedProjectList
      );
    }

    delete filter?.projectStatus;

    const responseData = await userReportService.getReportsOptimized(filter, page, perPage, sort);

    let finalResponse = {
      reportData: responseData.data,
      currentPage: Number(page),
      allRecordsCount: responseData.totalCount,
    };

    return res
      .status(HTTP_STATUS.OK)
      .json(successResponse(constantUtils.GET_REPORT, finalResponse));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Export pdf
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.exportReportPdf = async (req, res) => {
  try {
    let { userReportIds } = req.body;

    userReportIds = userReportIds.map(id => commonUtils.toObjectId(id));

    let filter = {
      _id: { $in: userReportIds },
      account: req.userData.account,
      deletedAt: null,
    };

    let response = await userReportService.getUserReportDetails(filter, userReportIds, true);

    response.forEach(item => {
      item.reportQuestions.forEach(question => {
        question.answerTypes.forEach(answerType => {
          const answerTitleIds = answerType.userAnswers.flatMap(userAnswer =>
            userAnswer.answers.map(answer => answer.answerTitleId.toString())
          );
          answerType.title = answerType.title.filter(title => {
            let commonIsPrint = title.isPrintable; // Default value for isPrintable

            // Check for user answers and isPrintable
            answerType.userAnswers.map(userAnswer => {
              const userAnswerData = userAnswer.answers.filter(answer => {
                return answer.answerTitleId.toString() === title._id.toString();
              });
              commonIsPrint =
                userAnswerData.length > 0 && 'isPrintable' in userAnswerData[0]
                  ? userAnswerData[0].isPrintable
                  : commonIsPrint;
            });
            // End of check for user answers and isPrintable

            const exists =
              (commonIsPrint && title.isRequired && title.isActive) ||
              (commonIsPrint &&
                !title.isRequired &&
                title.isActive &&
                answerTitleIds.includes(title._id.toString()));
            return exists;
          });
        });
      });
    });

    if (response.length === 0) {
      return res.status(400).json(errorResponse(constantUtils.USER_REPORT_NOT_EXIST));
    }

    const modifyedResponse = await this.modifyUserReportData(response[0]);

    // Generate PDF
    return await exportReportPDF(modifyedResponse, res);
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Modify User Report
 *
 * @param {*} requestData
 * @returns
 */
exports.modifyUserReportData = async requestData => {
  let { _id, reportQuestions } = requestData;
  let prepareRepoprtData = {};
  for (let key in _id) {
    if (['project', 'report', 'location'].includes(key)) {
      prepareRepoprtData[key] = _id[key].title;
    }
    prepareRepoprtData.assets = _id.asset;
    prepareRepoprtData.userReportStatus = _id.userReportStatus;
  }

  let reportQuestionData = [];
  for (let key in reportQuestions) {
    let prepareQuestionData = {};
    prepareQuestionData.title = `${reportQuestions[key].title}`;
    prepareQuestionData.supportedContent = reportQuestions[key].supportedContent;

    let answerData = this.prepareReportAnswers(reportQuestions[key].answerTypes);

    prepareQuestionData.answers = answerData;
    reportQuestionData.push(prepareQuestionData);
  }

  prepareRepoprtData.reportQuestions = reportQuestionData;
  return prepareRepoprtData;
};

/**
 * Prepare Report Answers
 *
 * @param {*} answerTypes
 * @returns
 */
exports.prepareReportAnswers = answerTypes => {
  const answerData = [];

  for (let answerKey of answerTypes) {
    const titleData = {
      type: answerKey.parameterType.uniqueKey,
      name: answerKey.parameterType.name,
      options: answerKey.option?.length ? answerKey.option : undefined,
    };

    const userAnswerData = [];
    const answerTitleMap = {}; // Replace Map with a plain object
    const answeredIds = []; // Replace Set with a plain array
    let isRequired, isPrintable;
    // Populate answerTitleMap with title values
    for (let title of answerKey.title) {
      answerTitleMap[title._id.toString()] = title.value;
      isPrintable = title.isPrintable;
      isRequired = title.isRequired;
    }

    // Populate user answers
    for (let userAnswer of answerKey.userAnswers) {
      for (let answer of userAnswer.answers) {
        const titleIdStr = answer.answerTitleId.toString();
        const matchedValue = answerTitleMap[titleIdStr];

        if (matchedValue) {
          if (!answeredIds.includes(titleIdStr)) {
            answeredIds.push(titleIdStr); // Track answered titles
          }
          userAnswerData.push({
            answerTitle: matchedValue,
            answer: answer.answer,
            isPrintable: isPrintable,
            isRequired: isRequired,
            _id: answer.answerTitleId,
          });
        }
      }
    }

    // Add unanswered titles
    for (let titleIdStr in answerTitleMap) {
      if (!answeredIds.includes(titleIdStr)) {
        userAnswerData.push({
          answerTitle: answerTitleMap[titleIdStr],
          answer: '',
          _id: titleIdStr,
        });
      }
    }

    userAnswerData.sort((a, b) => {
      const idA = a._id.toString();
      const idB = b._id.toString();

      return idA.localeCompare(idB);
    });

    titleData.answers = userAnswerData;
    answerData.push(titleData);
  }

  return answerData;
};

/**
 * Get User Report Details
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getUserReportDetails = async (req, res) => {
  try {
    let reqData = req.body;

    let userReportIds = [];

    // Check if userProjectReportId is present and use get method
    if ('userProjectReportId' in req.params) {
      const userReports = await userReportService.getUserReportByFilter({
        userProjectReport: req.params.userProjectReportId,
        account: req.userData.account,
        deletedAt: null,
      });

      if (userReports.length === 0) {
        return res.status(400).json(errorResponse(constantUtils.USER_REPORT_NOT_EXIST));
      }

      for (let userReport of userReports) {
        userReportIds.push(commonUtils.toObjectId(userReport._id));
      }
    } else {
      // Check if userReports is present and use post method
      for (let reportId of reqData.userReports) {
        if (!commonUtils.isValidId(reportId)) {
          return res.status(422).json(errorResponse(constantUtils.INVALID_REPORT_ID));
        } else {
          userReportIds.push(commonUtils.toObjectId(reportId));
        }
      }
    }

    let filter = {
      _id: { $in: userReportIds },
      account: req.userData.account,
      deletedAt: null,
    };

    const responseData = await userReportService.getUserReportDetails(filter, userReportIds);
    return res.status(200).json(successResponse(constantUtils.GET_REPORT, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Update User Report Status
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateUserReportStatus = async (req, res) => {
  try {
    let { userReports, status, ...rest } = req.body;
    let isUserReportUpdated = false;

    if (rest.signatureBy && !commonUtils.isValidId(rest.signatureBy)) {
      return res.status(422).json(errorResponse(constantUtils.INVALID_SIGNTURE_BY_ID));
    }

    for (let reportId of userReports) {
      if (commonUtils.isValidId(reportId)) {
        let updateData = {
          status,
          updatedAt: new Date(),
          updatedBy: req.userData._id,
        };

        if (rest.signature) {
          updateData.signature = rest.signature;
          updateData.signatureBy = rest.signatureBy || req.userData._id;
        }
        await userReportService.updateUserReportById(reportId, updateData);
        isUserReportUpdated = true;
      }
    }
    if (isUserReportUpdated) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res.status(200).json(successResponse(constantUtils.UPDATE_USER_REPORT));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Delete User Report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteUserReport = async (req, res) => {
  try {
    let { id } = req.params;

    const exist = await userReportService.searchUserReport({
      _id: id,
      deletedAt: null,
      account: req.userData.account,
    });
    if (!exist) {
      return res.status(400).json(errorResponse(constantUtils.USER_REPORT_NOT_EXIST));
    }

    const deletedReport = await userReportService.updateUserReportById(id, req.deletedAt);

    if (deletedReport) {
      await userReportAnswerService.updateUserReportAnswerManyByFilter(
        {
          userReport: deletedReport._id,
        },
        req.deletedAt
      );

      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res.status(200).json(successResponse(constantUtils.DELETE_USER_REPORT));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Delete Report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */

exports.deleteReport = async (req, res) => {
  try {
    let reqData = req.body;

    let userReportIds = [];

    for (let reportId of reqData.userReports) {
      if (!commonUtils.isValidId(reportId)) {
        return res.status(400).json(errorResponse(constantUtils.INVALID_REPORT_ID));
      } else {
        userReportIds.push(commonUtils.toObjectId(reportId));
      }
    }

    const deletedReport = await userReportService.deleteReportByIds(userReportIds, req.deletedAt);

    // Delete user report answers
    if (deletedReport) {
      await userReportAnswerService.updateUserReportAnswerManyByFilter(
        {
          userReport: { $in: userReportIds },
        },
        req.deletedAt
      );

      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res.status(200).json(successResponse(constantUtils.DELETE_REPORT));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * View report details for mobile
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getUserReportViewDetails = async (req, res) => {
  try {
    let reqData = req.body;

    const getUserReports = await userReportService.getUserReportByFilter({
      account: req.userData.account,
      deletedAt: null,
      ...reqData,
    });

    let userReportIds = [];

    getUserReports.map(item => {
      if (item) {
        userReportIds.push(item._id);
      }
    });

    if (userReportIds.length === 0) {
      return res.status(400).json(errorResponse(constantUtils.USER_REPORT_NOT_EXIST));
    }

    let filter = {
      _id: { $in: userReportIds },
      account: req.userData.account,
      deletedAt: null,
    };

    let responseData = await userReportService.getUserReportDetails(filter, userReportIds);
    responseData = await this.addAnswerTitleInUserReport(responseData);
    return res.status(200).json(successResponse(constantUtils.GET_REPORT, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

// Add answer title in user report
exports.addAnswerTitleInUserReport = async requestData => {
  for (let item of requestData) {
    for (let question of item.reportQuestions) {
      for (let answerData of question.answerTypes) {
        for (let userAnswer of answerData.userAnswers) {
          for (let answer of userAnswer.answers) {
            const title = answerData.title.find(
              title => title._id.toString() === answer.answerTitleId.toString()
            );
            if (title !== undefined) {
              answer.title = title.value || '';
            }
          }
        }
      }
    }
  }
  return requestData;
};

exports.getUserProjectReport = async (req, res) => {
  try {
    let reqData = req.body;

    const getUserProjectReport = await userProjectReportService.getSingleUserProjectReportByFilter({
      account: req.userData.account,
      deletedAt: null,
      ...reqData,
    });

    if (!getUserProjectReport) {
      return res.status(400).json(errorResponse(constantUtils.USER_PROJECT_REPORT_NOT_EXIST));
    }

    return res
      .status(200)
      .json(successResponse(constantUtils.GET_USER_PROJECT_REPORT, getUserProjectReport));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Create user web report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createUserWebReport = async (req, res) => {
  const session = await mongoose.startSession();
  const transactionOption = { ...transactionOptions };
  try {
    session.startTransaction(transactionOption);
    let { project, report, location, asset } = req.body;

    const userReportFilter = {
      project,
      report,
      location,
      asset,
    };

    const userReportData = await this.createUserReportData(userReportFilter, req, session);

    let { questions } = req.body;
    const userReport = userReportData._id;

    for (let question of questions) {
      const userAnswerData = {
        reportQuestion: question.reportQuestion,
        userReport,
        reportQuestionAnswers: question.reportQuestionAnswers,
        report,
      };

      await this.saveUserReportAnswer(userAnswerData, req, session);
    }

    let { status, signature } = req.body;
    const signatureData = {
      userReports: [userReport],
      signature,
      signatureBy: req.userData._id,
      status,
    };

    await this.updateUserReportStatusData(signatureData, req, res, session);

    await session.commitTransaction();
    session.endSession();

    if (userReportData) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res.status(200).json(successResponse(constantUtils.CREATE_USER_REPORT, userReportData));
  } catch (error) {
    await session.abortTransaction();
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Create user report Data
 *
 * @param {*} reqData
 * @param {*} req
 * @param {*} session
 * @returns
 */
exports.createUserReportData = async (reqData, req, session) => {
  reqData.userProjectReport = await userProjectReportService.checkAndCreateUserProjectReport(
    reqData,
    req.userData.account,
    req.userData._id,
    session
  );

  reqData.account = req.userData.account;
  reqData.createdBy = req.userData._id;

  const [userReportData] = await userReportService.createUserReport(reqData, session);

  return userReportData;
};

/**
 * save UserReport Answer
 *
 * @param {*} UserAnswerData
 * @param {*} req
 * @param {*} session
 * @returns
 */
exports.saveUserReportAnswer = async (userAnswerData, req, session) => {
  let { reportQuestion, report, userReport, reportQuestionAnswers } = userAnswerData;
  for (let element of reportQuestionAnswers) {
    let { id, answers } = element;

    // Check Report Question Answer has Printable value
    answers = await checkReportQuestionAnswer(id, answers, userReport, session);
    let filter = {
      reportQuestion,
      report,
      userReport,
      reportQuestionAnswer: id,
      account: req.userData.account,
      createdBy: req.userData._id,
      deletedAt: null,
    };

    const exists = await userReportAnswerService.getSingleUserReportAnswer(filter);
    if (exists) {
      // Check Is Printable exist in user report answer
      answers = await checkIsPrintableValueInExistingAnswer(exists, answers);
      // Update Answers
      let updateData = {
        answers,
        updatedBy: req.userData._id,
        updatedAt: new Date(),
      };

      await userReportAnswerService.updateUserReportAnswer(exists._id, updateData, session);
    } else {
      // Create Answers
      let prepareAnswerData = {
        reportQuestion,
        report,
        userReport,
        reportQuestionAnswer: id,
        answers,
        account: req.userData.account,
        createdBy: req.userData._id,
        createdAt: new Date(),
      };

      await userReportAnswerService.createUserReportAnswer(prepareAnswerData, session);
    }
  }
};

/**
 * Update user report status data
 *
 * @param {*} signatureData
 * @param {*} req
 * @param {*} res
 * @param {*} session
 * @returns
 */
exports.updateUserReportStatusData = async (signatureData, req, res, session) => {
  let { userReports, status, ...rest } = signatureData;

  if (rest.signatureBy && !commonUtils.isValidId(rest.signatureBy)) {
    await session.abortTransaction();
    session.endSession();

    return res.status(422).json(errorResponse(constantUtils.INVALID_SIGNTURE_BY_ID));
  }

  for (let reportId of userReports) {
    if (commonUtils.isValidId(reportId)) {
      let updateData = {
        status,
        updatedAt: new Date(),
        updatedBy: req.userData._id,
      };

      if (rest.signature) {
        updateData.signature = rest.signature;
        updateData.signatureBy = rest.signatureBy || req.userData._id;
      }

      await userReportService.updateUserReportById(reportId, updateData, session);
    }
  }
};

/**
 * Get All User Reports Except Request User
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getAllUserReportsExceptRequestUser = async (req, res) => {
  try {
    let { status } = req.query;
    // get assign projects from member table
    const memberData = await memberService.getMemberByFilter({
      user: req.userData._id,
      account: req.userData.account,
      deletedAt: null,
    });

    let projectIds = [];

    // add project ids
    memberData.map(item => {
      if (item) {
        projectIds.push(item.project);
      }
    });
    // get user reports from user report table
    const getUserReports = await userReportService.getUserReportByFilter({
      ...(status && { status }),
      project: { $in: projectIds },
      // createdBy: { $ne: req.userData._id },
      account: req.userData.account,
      deletedAt: null,
    });

    let userReportIds = [];

    // add user report ids
    getUserReports.map(item => {
      if (item) {
        userReportIds.push(item._id);
      }
    });
    if (userReportIds.length === 0) {
      return res.status(400).json(errorResponse(constantUtils.USER_REPORT_NOT_EXIST));
    }

    let filter = {
      _id: { $in: userReportIds },
      account: req.userData.account,
      deletedAt: null,
    };

    // get user reports with answers
    let responseData = await userReportService.getAllUserReportsWithAnswers(filter);
    responseData = await this.addAnswerTitleInUserReport(responseData); // add answer title in
    responseData = await this.filterUserReportData(responseData); // filter user report data
    return res.status(200).json(successResponse(constantUtils.GET_USER_REPORTS, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.filterUserReportData = async requestData => {
  let payload = [];
  for (let item of requestData) {
    for (let question of item.reportQuestions) {
      for (let answerType of question.answerTypes) {
        const requiredAnswerTitlesLength = answerType.title.length;

        // Sum all userAnswer.answers lengths for this answerType
        const totalUserAnswers = answerType.userAnswers.reduce((sum, userAnswer) => {
          return sum + userAnswer.answers.length;
        }, 0);

        const reportId = item?._id?.report?._id?.toString();

        if (requiredAnswerTitlesLength > totalUserAnswers && !payload.includes(reportId)) {
          payload.push(reportId);
        }
      }
      // question.answerTypes = question.answerTypes.map(answerType => {
      //   return {
      //     ...answerType,
      //     userAnswers: answerType.userAnswers.filter(
      //       ua =>
      //         ua.createdBy &&
      //         ua.createdBy._id.toString() !== loggedInUserId.toString() &&
      //         !['admin', 'superadmin'].includes(ua.createdBy.role?.title)
      //     ),
      //   };
      // });
    }
  }

  let newRequestData = requestData.filter(item => {
    let reportId = item._id?.report?._id?.toString();
    return payload.includes(reportId);
  });

  return newRequestData;
};

exports.commonUpdateSyncApiManage = async account => {
  await commonfunctionUtils.updateSyncApiManage({
    syncApis: ['reportUsers', 'reportConfig', 'allUserReports', 'reportNewFormConfig'],
    account,
  });
};
