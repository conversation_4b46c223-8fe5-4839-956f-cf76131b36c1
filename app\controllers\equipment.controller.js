// Services
const equipmentService = require('../services/equipment.service');
const inventoryHistoryService = require('../services/inventory-history.service');
const dprService = require('../services/dpr.service');
const projectEquipmentService = require('../services/project-equipment-type.service');
const pmOrderManageEquipmentService = require('../services/pm-order-manage-equipment.service');
const equipmentOrderHistoryService = require('../services/equipment-order-history.service');
const equipmentTypeService = require('../services/equipment-type.service');
const equipmentOrderService = require('../services/equipment-order.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const equipmentUtils = require('../utils/equipment.utils');
const commonUtils = require('../utils/common.utils');
const { validateSearch } = require('../utils/common-function.utils');
const commonFunctionsUtils = require('../utils/common-function.utils');
const exportExcelUtils = require('../utils/export-excel.util');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Create Equipment
 *
 * @param {*} req
 * @param {*} res
 */
exports.createEquipment = async (req, res) => {
  try {
    const requestData = req.body;
    requestData.equipmentNumber = await equipmentService.productNumberGenerator(
      requestData.equipmentType
    );
    requestData.value =
      typeof requestData.value !== 'undefined'
        ? requestData.value
        : await equipmentService.checkEquipmentValue(requestData.equipmentType);
    if (requestData.certificateType) {
      await equipmentUtils.updateCertificateNames(requestData);
    }
    requestData.equipmentImage = requestData?.equipmentImage ? requestData.equipmentImage : null;

    //add default value
    requestData.equipmentCurrentLocation = global.constant.DEFAULT_EQUIPMENT_CURRENT_LOCATION;
    requestData.equipmentLocationFromDate = new Date();

    // Create Equipment
    const response = await equipmentService.createEquipment(requestData);

    const equipmentData = await equipmentService.getSingleEquipmentByFilter({
      _id: response._id,
      account: req.userData.account,
    });

    // create default serial number data
    if (response && response.quantity > 0) {
      // create inventory history
      let purchaseOrderNumber = commonUtils.generateOrderNumber(10, 'purchase');
      await inventoryHistoryService.prepareInventoryHistoryAndCreate(
        response._id,
        'in-stock',
        'purchase',
        equipmentData.warehouse.name,
        purchaseOrderNumber,
        response.quantity,
        'in',
        null,
        req.userData.account,
        req.userData._id
      );
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.CREATE_EQUIPMENT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Equipments
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipments = async (req, res) => {
  try {
    let { account } = req.userData;
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? { createdAt: 1 } : { createdAt: -1 };
    let { search, equipmentType } = req.query;
    search = await validateSearch(search);

    if (!search && search !== '') {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }

    if (req.query?.sortName) {
      sort = req.query.sortName === 'asc' ? { name: 1 } : { name: -1 };
    }

    let filterData = {
      account: account,
      deletedAt: null,
    };

    if (search) {
      filterData = {
        $and: [
          {
            $or: [
              { name: { $regex: search, $options: 'i' } },
              { serialNumber: { $regex: search, $options: 'i' } },
              { equipmentNumber: { $regex: search, $options: 'i' } },
              { 'equipmentType.type': { $regex: search, $options: 'i' } },
              { 'equipmentType.equipmentCategory.name': { $regex: search, $options: 'i' } },
            ],
          },
          filterData,
        ],
      };
    }

    if ('warehouse' in req.query) {
      filterData.warehouse = req.query.warehouse;
    }

    if ('qrCode' in req.query) {
      filterData['qrCode.code'] = req.query.qrCode;
    }

    if (equipmentType) {
      let equipmentTypeData = equipmentType.split(',');
      filterData.equipmentType = { $in: equipmentTypeData };
    }

    // filter missing qr and certificate
    if ('missing' in req.query) {
      let { missing } = req.query;
      missing = missing.split(',');
      for (let key in missing) {
        filterData = await this.missingEquipmentSearchFilter(missing[key], filterData);
      }
    }
    const response = await equipmentService.getEquipments(filterData, page, perPage, sort);
    res.status(200).json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Equipment by Id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentById = async (req, res) => {
  try {
    let { id } = req.params;
    let filter = { _id: id, account: req.userData.account, deletedAt: null };
    const response = await equipmentService.getSingleEquipmentByFilter(filter);
    if (!response) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.EQUIPMENT_NOT_FOUND));
    }

    // check field is exists or not and add default value
    if (!response?.equipmentRow || response?.equipmentRow === '') {
      response.equipmentRow = response?.equipmentLocationInWarehouse || '';
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Equipment by QRCode
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getEquipmentByQRCode = async (req, res) => {
  try {
    let { qrCode } = req.params;
    const response = await equipmentService.findQRCode(qrCode);
    if (response.length === 0) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.EQUIPMENT_NOT_FOUND));
    }

    // check QR code is outdated
    if (!(await equipmentService.checkQRCodeOutDated(response, qrCode)))
      return res.status(400).json(responseUtils.errorResponse(constantUtils.OUTDATED_QR_CODE));

    res.status(200).json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT, response[0]));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Equipment
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateEquipment = async (req, res) => {
  try {
    let { id } = req.params;
    const requestData = req.body;
    let { qrCode } = req.body;
    const response = await equipmentService.getSingleEquipmentByFilter({ _id: id });
    if (!response) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.EQUIPMENT_NOT_FOUND));
    }

    // Check Equipment Type And Equipment Number
    if (requestData?.equipmentType) {
      requestData.equipmentNumber = response.equipmentNumber;

      // Check Equipment Value
      requestData.value =
        typeof requestData.value !== 'undefined'
          ? requestData.value
          : await equipmentService.checkEquipmentValue(requestData.equipmentType);
    }

    if (requestData.certificateType) {
      await equipmentUtils.updateCertificateNames(requestData);
    }

    if (requestData.qrCode) {
      let exist = await equipmentService.checkUniqueQrCode({
        _id: { $ne: id },
        'qrCode.code': qrCode[0].code,
        account: req.userData.account,
        deletedAt: null,
      });

      if (exist) {
        return res.status(400).json(responseUtils.errorResponse(constantUtils.DUPLICATE_QR_CODE));
      }
    }

    requestData.updatedBy = req.userData._id;
    requestData.updatedAt = new Date();

    const responseUpdate = await equipmentService.updateEquipment(id, requestData);

    // update inventory history - remove below condition after client updates
    if (responseUpdate && req.params?.tempType === 'temp') {
      await inventoryHistoryService.updateInventoryHistoryByFilter(
        {
          equipment: id,
          status: 'in-stock',
          type: 'purchase',
          inOut: 'in',
          account: req.userData.account,
          deletedAt: null,
        },
        {
          $set: {
            ...(requestData?.quantity && { quantity: requestData.quantity }),
            updatedAt: new Date(),
            updatedBy: req.userData._id,
          },
        }
      );
    }

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_EQUIPMENT, responseUpdate));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Equipment
 *
 * @param {*} req
 * @param {*} res
 */
exports.deleteEquipment = async (req, res) => {
  try {
    let { id } = req.params;
    const response = await equipmentService.getSingleEquipmentByFilter({ _id: id });
    if (!response) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.EQUIPMENT_NOT_FOUND));
    }
    const responseDelete = await equipmentService.deleteEquipment(id, req.deletedAt);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_EQUIPMENT, responseDelete));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Equipment Front Search
 *
 * @param {*} req
 * @param {*} res
 */
exports.equipmentFrontSearch = async (req, res) => {
  try {
    let data = await this.getInventoryListData(req, res);

    const response = await equipmentService.checkEquipmentLocation(data.inventoryData);
    data.inventoryData = [...response];

    res.status(200).json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT, data));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Bind QR Code with Equipment
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.bindQRCode = async (req, res) => {
  try {
    let { equipment, qrCode, isSystemGenerated } = req.body;
    let requestData = {
      code: qrCode,
      isActive: true,
      isSystemGenerated,
      createdDate: new Date(),
      createdBy: req.userData._id,
    };

    const getQRCode = await equipmentService.findQRCode(qrCode);

    if (getQRCode.length > 0) {
      let message = `${constantUtils.QR_CODE_ALREADY_BINDED} - ${getQRCode[0].name}`;
      return res.status(400).json(responseUtils.errorResponse(message));
    }

    const response = await equipmentService.bindQRCode(equipment, requestData);
    res.status(200).json(responseUtils.successResponse(constantUtils.BIND_QR_CODE, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Missing Equipment Search Filter
 *
 * @param {*} requestParam
 * @param {*} filterData
 * @returns
 */
exports.missingEquipmentSearchFilter = async (requestParam, filterData) => {
  try {
    let defaultField = '';
    let defaultStatement = {};
    switch (requestParam) {
      case 'all':
        filterData = {
          $and: [
            {
              $or: [
                { qrCode: { $in: [null, []] } },
                { certificateType: { $in: [null, []] } },
                { 'certificateType.endDate': { $lt: new Date() } },
                {
                  'certificateType.endDate': {
                    $gte: new Date(),
                    $lt: new Date(new Date().setDate(new Date().getDate() + 60)),
                  },
                },
              ],
            },
            filterData,
          ],
        };
        break;
      case 'qr':
        defaultField = 'qrCode';
        defaultStatement = { $in: [null, []] };
        filterData = await this.modifyMissingQueryOnMultiSelect(
          filterData,
          defaultField,
          defaultStatement
        );
        break;
      case 'certificate':
        defaultField = 'certificateType';
        defaultStatement = { $in: [null, []] };
        filterData = await this.modifyMissingQueryOnMultiSelect(
          filterData,
          defaultField,
          defaultStatement
        );
        break;
      case 'certificate_expired':
        defaultField = 'certificateType.endDate';
        defaultStatement = { $lt: new Date() };
        filterData = await this.modifyMissingQueryOnMultiSelect(
          filterData,
          defaultField,
          defaultStatement
        );
        break;
      case 'certificate_expire_in_30':
        defaultField = 'certificateType.endDate';
        defaultStatement = {
          $gte: new Date(),
          $lt: new Date(new Date().setDate(new Date().getDate() + 30)),
        };
        filterData = await this.modifyMissingQueryOnMultiSelect(
          filterData,
          defaultField,
          defaultStatement
        );
        break;
      case 'certificate_expire_in_60':
        defaultField = 'certificateType.endDate';
        defaultStatement = {
          $gte: new Date(),
          $lt: new Date(new Date().setDate(new Date().getDate() + 60)),
        };
        filterData = await this.modifyMissingQueryOnMultiSelect(
          filterData,
          defaultField,
          defaultStatement
        );
        break;
    }
    return filterData;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * modify query on multi select
 *
 * @param {*} filterData
 * @param {*} fieldValue
 * @param {*} orDefaultValue
 * @returns
 */
exports.modifyMissingQueryOnMultiSelect = async (filterData, fieldValue, orDefaultValue) => {
  try {
    let modifyDefaultValue = {};
    modifyDefaultValue[fieldValue] = orDefaultValue;
    if (
      filterData['certificateType.endDate'] !== undefined ||
      filterData['certificateType'] !== undefined ||
      filterData['qrCode'] !== undefined
    ) {
      let modifiedFilterData = {
        $and: [
          {
            $or: [modifyDefaultValue],
          },
        ],
      };

      const filterFields = ['certificateType.endDate', 'certificateType', 'qrCode'];

      filterFields.forEach(field => {
        if (filterData[field] !== undefined) {
          modifiedFilterData['$and'][0]['$or'].push({ [field]: filterData[field] });
          delete filterData[field];
        }
      });
      modifiedFilterData['$and'].push(filterData);
      filterData = modifiedFilterData;
    } else if (filterData['$and'] !== undefined) {
      filterData['$and'][0]['$or'].push(modifyDefaultValue);
    } else {
      filterData[fieldValue] = orDefaultValue;
    }
    return filterData;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * Update Additional Stock
 *
 * @param {*} req
 * @param {*} res
 */
exports.additionalStock = async (req, res) => {
  try {
    let reqData = await equipmentUtils.transformReqData(req.body);
    for (const element of reqData) {
      let equipmentData = await equipmentService.getSingleEquipmentByFilter({
        _id: element.equipment,
      });

      let type = 'purchase';
      let inOut = 'in';

      let reqQuantity = parseInt(element.quantity);
      if (reqQuantity < equipmentData.quantity) {
        type = 'cancel';
        inOut = 'out';
      }

      const response = await equipmentService.updateEquipment(element.equipment, {
        quantity: element.quantity,
        updatedBy: req.userData._id,
      });
      if (response) {
        let purchaseOrderNumber = commonUtils.generateOrderNumber(10, type);
        await inventoryHistoryService.prepareInventoryHistoryAndCreate(
          equipmentData._id,
          'in-stock',
          type,
          equipmentData.warehouse.name,
          purchaseOrderNumber,
          element.quantity,
          inOut,
          null,
          req.userData.account,
          req.userData._id
        );
      }
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.UPDATE_EQUIPMENT));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.buildFilterData = async (
  req,
  search,
  name,
  serialNumber,
  equipmentNumber,
  type,
  equipmentCategory,
  account
) => {
  let filterData = {
    account: account,
    deletedAt: null,
  };
  if (search) {
    filterData = {
      $and: [
        {
          $or: [
            { name: { $regex: search, $options: 'i' } },
            { serialNumber: { $regex: search, $options: 'i' } },
            { equipmentNumber: { $regex: search, $options: 'i' } },
            { 'equipmentType.type': { $regex: search, $options: 'i' } },
            { 'equipmentType.equipmentCategory.name': { $regex: search, $options: 'i' } },
          ],
        },
        filterData,
      ],
    };
  } else {
    filterData = {
      ...(name !== null && { name: { $regex: name, $options: 'i' } }),
      ...(serialNumber !== null && { serialNumber: { $regex: serialNumber, $options: 'i' } }),
      ...(equipmentNumber !== null && {
        equipmentNumber: { $regex: equipmentNumber, $options: 'i' },
      }),
      ...(type !== null && {
        'equipmentType.type': { $regex: type, $options: 'i' },
      }),
      ...(equipmentCategory !== null && {
        'equipmentType.equipmentCategory.name': { $regex: equipmentCategory, $options: 'i' },
      }),
    };
  }

  if ('warehouse' in req.query) {
    filterData.warehouse = commonUtils.toObjectId(req.query.warehouse);
  }
  return filterData;
};

/**
 * get inventory excel
 *
 * @param {*} req
 * @param {*} res
 */
exports.getInventoryExcel = async (req, res) => {
  try {
    const { timeZone } = req.body;
    const data = await this.getInventoryListData(req, res);

    const tableData = await commonFunctionsUtils.getTableData(
      data.inventoryData,
      commonUtils.inventoryTableHeaders,
      commonUtils.inventoryRowMapper
    );

    await exportExcelUtils.exportInventoryExcel(
      res,
      'inventory',
      tableData.columns,
      tableData.rows,
      timeZone
    );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * get inventory list
 *
 * @param {*} req
 * @param {*} res
 */
exports.getInventoryListData = async (req, res) => {
  try {
    let { account } = req.userData;
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? { createdAt: 1 } : { createdAt: -1 };
    let {
      search,
      equipmentType,
      id,
      name = null,
      serialNumber = null,
      equipmentNumber = null,
      type = null,
      equipmentCategory = null,
      condition,
    } = req.query;
    let equipmentTypes;
    let defaultFilter = { account: account, deletedAt: null };
    search = await validateSearch(search);

    if (!search && search !== '') {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }

    if (req.query?.sortName) {
      sort = req.query.sortName === 'asc' ? { name: 1 } : { name: -1 };
    }

    const filterData = await this.buildFilterData(
      req,
      search,
      name,
      serialNumber,
      equipmentNumber,
      type,
      equipmentCategory,
      account
    );

    if (equipmentType) {
      let equipmentTypeData = equipmentType.split(',');
      equipmentTypes = equipmentTypeData.map(equipmentType =>
        commonUtils.toObjectId(equipmentType)
      );

      defaultFilter.equipmentType = { $in: equipmentTypes };
    }

    // Filter by equipment ID if provided
    if (id) {
      if (commonUtils.isValidId(id)) {
        defaultFilter._id = commonUtils.toObjectId(id);
      } else {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(responseUtils.errorResponse(constantUtils.INVALID_EQUIPMENT_ID));
      }
    }

    // Filter missing QR and certificate
    if ('missing' in req.query) {
      let { missing } = req.query;
      missing = missing.split(',');

      if (missing.includes('certificate')) {
        filterData['equipmentType.certificateTypes'] = { $exists: true, $ne: [] };
      }
      for (let key in missing) {
        defaultFilter = await this.missingEquipmentSearchFilter(missing[key], defaultFilter);
      }
    }

    // Add filter for condition [ok, maintenance, write-off]
    defaultFilter = {
      ...defaultFilter,
      condition: condition ? { $in: condition.split(',') } : { $ne: 'write-off' },
    };

    let data = {
      currentPage: Number(page),
    };

    if (type || equipmentCategory) {
      const [inventoryData] = await equipmentService.equipmentSearchByKeyword(
        defaultFilter,
        page,
        perPage,
        sort,
        filterData
      );

      data.allRecordsCount =
        inventoryData.metadata.length === 0 ? 0 : inventoryData.metadata[0].totalDocuments;
      data.inventoryData = inventoryData.data;
    } else {
      defaultFilter = { ...defaultFilter, ...filterData };
      const response = await equipmentService.equipmentSearchByKeyword(
        defaultFilter,
        page,
        perPage,
        sort
      );
      data.inventoryData = response;
      data = await commonUtils.getCountFromQuery('equipment', defaultFilter, data);
    }

    return data;
  } catch (error) {
    return res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Multiple Type Equipment Quantity Detail
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getMultipleTypeEquipmentDetail = async (req, res) => {
  try {
    let { id } = req.params;
    let filter = { _id: id, account: req.userData.account, deletedAt: null };
    let response = await equipmentService.getSingleEquipmentByFilter(filter);
    if (!response) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.EQUIPMENT_NOT_FOUND));
    }
    response = await equipmentService.checkMultipleTypeEquipmentDetail(response._id);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.EQUIPMENT_QUANTITY_DETAILS, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get equipment details summary
 *
 * @param {Object} req
 * @param {Object} res
 * @returns
 */
exports.getEquipmentSummary = async (req, res) => {
  try {
    const { dprId } = req.params;

    const dpr = await dprService.getLatestDpr({
      _id: dprId,
      account: req.userData.account,
      deletedAt: null,
    });

    if (!dpr) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_DPR));
    }

    let result = await projectEquipmentService.projectEquipmentTypeList({
      project: commonUtils.toObjectId(dpr.project),
      account: req.userData.account,
      deletedAt: null,
    });

    let equipmentTypeIds = result.map(
      projectEquipmentType => projectEquipmentType.equipmentType._id
    );

    const response = await equipmentService.getEquipmentSummary(
      req.userData.account,
      commonUtils.toObjectId(dpr.project),
      dpr.dprDate,
      equipmentTypeIds
    );

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.EQUIPMENT_SUMMARY, response));
  } catch (error) {
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};

/**
 * Update Equipment Type
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateEquipmentType = async (req, res) => {
  try {
    const { id } = req.params;
    const { oldEquipmentType, newEquipmentType } = req.body;

    if (!commonUtils.isValidId(oldEquipmentType) || !commonUtils.isValidId(newEquipmentType)) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_ID));
    }

    let orderExists = await pmOrderManageEquipmentService.getPMOrderManageEquipment({
      equipmentType: oldEquipmentType,
      account: req.userData.account,
      deletedAt: null,
    });

    if (orderExists.length > 0) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_TYPE_IN_USE));
    }

    const equipmentOrderExist = await equipmentOrderService.getEquipmentOrders({
      account: req.userData.account,
      equipmentType: oldEquipmentType,
      deletedAt: null,
      status: { $in: ['pending', 'queue', 'rejected'] },
    });

    if (equipmentOrderExist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.MOVE_EQUIPMENT_TYPE_TO_SUITABLE_STATUS));
    }

    const getEquipmentType = await equipmentTypeService.getEquipmentType({
      _id: { $in: [oldEquipmentType, newEquipmentType] },
      account: req.userData.account,
      deletedAt: null,
    });

    if (getEquipmentType.length === 0) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_TYPE_NOT_FOUND));
    }

    await pmOrderManageEquipmentService.updatePMOrderManageEquipmentByPMOrderId(
      {
        equipment: { $in: [id] },
        equipmentType: oldEquipmentType,
        deletedAt: null,
      },
      {
        equipmentType: newEquipmentType,
      }
    );

    await equipmentOrderHistoryService.updateEquipmentHistoryByFilter(
      {
        equipment: id,
        equipmentType: oldEquipmentType,
        deletedAt: null,
      },
      { equipmentType: newEquipmentType }
    );

    const response = await equipmentService.updateEquipment(id, {
      equipmentType: newEquipmentType,
    });

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.UPDATE_EQUIPMENT_TYPE, response));
  } catch (error) {
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};

/**
 * Get Equipment Summary View
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentSummaryView = async (req, res) => {
  try {
    const {
      page = 0,
      perPage = 25,
      sort = 'desc',
      equipmentCategory = null,
      equipmentType = null,
      priceType = null,
      quantityType = null,
    } = req.query;

    // Parse numbers
    const pageNum = Number(page) || 0;
    const perPageNum = Number(perPage) || 25;
    const sortOrder = sort === 'asc' ? 1 : -1;

    // Build filter
    const filter = {
      account: req.userData.account,
      deletedAt: null,
      ...(equipmentCategory && { name: { $regex: equipmentCategory, $options: 'i' } }),
    };

    const { InventorySummary, totalCount } = await equipmentService.getEquipmentSummaryView(
      filter,
      pageNum,
      perPageNum,
      sortOrder,
      equipmentType,
      priceType,
      quantityType
    );
    return res.status(HTTP_STATUS.OK).json(
      responseUtils.successResponse(constantUtils.EQUIPMENT_SUMMARY_VIEW, {
        InventorySummary,
        totalCount,
      })
    );
  } catch (error) {
    return res
      .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
      .json(responseUtils.errorResponse(error.message));
  }
};
